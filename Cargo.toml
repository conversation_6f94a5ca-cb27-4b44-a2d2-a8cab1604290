[workspace]
members = ["crates/*"]
resolver = "3"

[workspace.package]
version = "0.0.1"
edition = "2024"

[workspace.dependencies]
straylight-common = { path = "crates/common" }
straylight-core = { path = "crates/core" }
straylight-egress = { path = "crates/egress" }
straylight-ingress = { path = "crates/ingress" }

tracing = "0.1"
futures = "0.3.31"
tokio = { version = "1.45.0", features = ["net", "time"] }
secrecy = "0.10.3"

kanal = "0.1"

tokio-tungstenite = { version = "0.26.2", features = ["native-tls"] }

okx-rs = { path = "./vendor/okx-rs" }

[package]
name = "straylight-okx-gateway"
version.workspace = true
edition.workspace = true

[[bin]]
name = "straylight-okx-gateway"
path = "okx-gateway/main.rs"

[dependencies]
straylight-common.workspace = true
straylight-core.workspace = true
straylight-ingress.workspace = true
straylight-egress.workspace = true

tokio = { workspace = true, features = ["rt", "rt-multi-thread", "macros"] }
futures.workspace = true

tracing.workspace = true
tracing-subscriber = "0.3"
kanal.workspace = true

r3bl_tui = "0.7.1"
