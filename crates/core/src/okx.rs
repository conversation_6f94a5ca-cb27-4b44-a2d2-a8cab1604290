use okx_rs::{
    api::v5::{
        AccountChannel, ApiResponse, BalanceAndPositionChannel, CancelOrder, GetOrderList,
        PlaceOrder, Request, WsResponse,
        orderbook_trading::{fill::websocket::FillsChannel, orders::websocket::OrdersChannel},
    },
    websocket::WebsocketChannel,
};

use serde::Deserialize;
use serde_json::Value;

pub type AccountUpdate<'a> = <AccountChannel as WebsocketChannel>::Response<'a>;
pub type BalanceAndPositionUpdate<'a> =
    <BalanceAndPositionChannel as WebsocketChannel>::Response<'a>;
pub type OrdersUpdate<'a> = <OrdersChannel as WebsocketChannel>::Response<'a>;
pub type FillsUpdate<'a> = <FillsChannel as WebsocketChannel>::Response<'a>;
pub type PlaceOrderResponse = <PlaceOrder as Request>::Response;
pub type CancelOrderResponse = <CancelOrder as Request>::Response;
pub type ModifyOrderResponse = <PlaceOrder as Request>::Response;

#[derive(Debug, Deserialize)]
#[serde(untagged)]
pub enum OkxWsMessage<'a> {
    #[serde(borrow)]
    Account(AccountUpdate<'a>),
    #[serde(borrow)]
    BalanceAndPosition(BalanceAndPositionUpdate<'a>),
    #[serde(borrow)]
    Orders(OrdersUpdate<'a>),
    #[serde(borrow)]
    Fills(FillsUpdate<'a>),
    PlaceOrder(PlaceOrderResponse),
    CancelOrder(CancelOrderResponse),
    ModifyOrder(ModifyOrderResponse),
}

pub type OrderListResponse = <GetOrderList as Request>::Response;

#[derive(Debug, Deserialize)]
#[serde(untagged)]
pub enum OkxRestMessage {
    OrderList(OrderListResponse),
}

#[derive(Debug, Deserialize)]
#[serde(untagged)]
pub enum OkxMessage<'a> {
    #[serde(borrow)]
    Ws(WsResponse<'a, Value, OkxWsMessage<'a>>),
    Rest(ApiResponse<OkxRestMessage>),
}
