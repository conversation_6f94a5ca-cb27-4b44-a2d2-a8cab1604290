use std::convert::Infallible;

use okx_rs::api::v5::{ApiResponse, WsResponse};
use thiserror::Error;

use crate::okx::{
    AccountUpdate, BalanceAndPositionUpdate, CancelOrderResponse, FillsUpdate, ModifyOrderResponse,
    OkxMessage, OkxRestMessage, OkxWsMessage, OrderListResponse, OrdersUpdate, PlaceOrderResponse,
};

pub trait OkxAccountUpdateHandler {
    type Error: std::fmt::Debug;

    fn on_account(&self, update: &AccountUpdate) -> Result<(), Self::Error>;
    fn on_balance_and_position(&self, update: &BalanceAndPositionUpdate)
    -> Result<(), Self::Error>;
    fn on_orders(&self, update: &OrdersUpdate) -> Result<(), Self::Error>;
    fn on_fills(&self, update: &FillsUpdate) -> Result<(), Self::Error>;
    fn on_place_order(&self, update: &PlaceOrderResponse) -> Result<(), Self::Error>;
    fn on_cancel_order(&self, update: &CancelOrderResponse) -> Result<(), Self::Error>;
    fn on_modify_order(&self, update: &ModifyOrderResponse) -> Result<(), Self::Error>;
    fn on_order_list(&self, update: &OrderListResponse) -> Result<(), Self::Error>;
}

pub struct OkxAccountUpdateDispatcher<'a, T> {
    handler: &'a mut T,
}

impl<'a, T: OkxAccountUpdateHandler> OkxAccountUpdateDispatcher<'a, T> {
    pub fn new(handler: &'a mut T) -> Self {
        Self { handler }
    }
}

#[derive(Debug, Error)]
pub enum DispatcherError<E> {
    #[error("handler failed to process message: {0}")]
    Handler(#[from] E),
    #[error("unknown message, unable to dispatch")]
    UnknownMessage,
    #[error("failed to deserialize message: {0}")]
    Deserialization(serde_json::Error),
}

impl<'a, T> OkxAccountUpdateDispatcher<'a, T>
where
    T: OkxAccountUpdateHandler,
{
    pub fn dispatch(&mut self, raw: &[u8]) -> Result<(), DispatcherError<T::Error>> {
        let update: OkxMessage =
            serde_json::from_slice(raw).map_err(DispatcherError::Deserialization)?;

        match update {
            OkxMessage::Ws(WsResponse { data: Some(ws), .. }) => match ws {
                OkxWsMessage::Account(update) => self.handler.on_account(&update)?,
                OkxWsMessage::BalanceAndPosition(update) => {
                    self.handler.on_balance_and_position(&update)?
                }
                OkxWsMessage::Orders(update) => self.handler.on_orders(&update)?,
                OkxWsMessage::Fills(update) => self.handler.on_fills(&update)?,
                OkxWsMessage::PlaceOrder(update) => self.handler.on_place_order(&update)?,
                OkxWsMessage::CancelOrder(update) => self.handler.on_cancel_order(&update)?,
                OkxWsMessage::ModifyOrder(update) => self.handler.on_modify_order(&update)?,
            },
            OkxMessage::Rest(ApiResponse {
                data: Some(rest), ..
            }) => match rest {
                OkxRestMessage::OrderList(update) => self.handler.on_order_list(&update)?,
            },
            _ => {
                tracing::error!("unable to dispatch message");
                return Err(DispatcherError::UnknownMessage);
            }
        };

        Ok(())
    }
}

impl OkxAccountUpdateHandler for () {
    type Error = Infallible;

    fn on_account(&self, _: &AccountUpdate) -> Result<(), Self::Error> {
        Ok(())
    }

    fn on_balance_and_position(&self, _: &BalanceAndPositionUpdate) -> Result<(), Self::Error> {
        Ok(())
    }

    fn on_orders(&self, _: &OrdersUpdate) -> Result<(), Self::Error> {
        Ok(())
    }

    fn on_fills(&self, _: &FillsUpdate) -> Result<(), Self::Error> {
        Ok(())
    }

    fn on_place_order(&self, _: &PlaceOrderResponse) -> Result<(), Self::Error> {
        Ok(())
    }

    fn on_cancel_order(&self, _: &CancelOrderResponse) -> Result<(), Self::Error> {
        Ok(())
    }

    fn on_modify_order(&self, _: &ModifyOrderResponse) -> Result<(), Self::Error> {
        Ok(())
    }

    fn on_order_list(&self, _: &OrderListResponse) -> Result<(), Self::Error> {
        Ok(())
    }
}
