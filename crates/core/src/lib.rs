use bytes::Bytes;
use serde_json::Value;
use straylight_common::messages::{CorePayload, EgressPayload, OkxExecuteApi};

pub mod dispatch;
use dispatch::OkxAccountUpdateDispatcher as Dispatcher;

pub mod okx;

pub struct Core<'a, H> {
    rx: kanal::AsyncReceiver<CorePayload>,
    tx: kanal::AsyncSender<EgressPayload>,

    user_commands: kanal::AsyncReceiver<String>,
    user_output: kanal::AsyncSender<String>,

    dispatcher: Dispatcher<'a, H>,
}

const PLACE_ORDER_SAMPLE: &'static str = r#"{
  "id": "1512",
  "op": "order",
  "args": [
    {
      "side": "buy",
      "instId": "BTC-USDT",
      "tdMode": "isolated",
      "ordType": "market",
      "sz": "100"
    }
  ]
}"#;

const CANCEL_ORDER_SAMPLE: &'static str = r#"{
  "id": "1513",
  "op": "cancel-order",
  "args": [
    {
      "instId": "BTC-USDT",
      "ordId": "****************"
    }
  ]
}"#;

const MODIFY_ORDER_SAMPLE: &'static str = r#"{
  "id": "1512",
  "op": "amend-order",
  "args": [
    {
      "instId": "BTC-USDT",
      "ordId": "****************",
      "newSz": "2"
    }
  ]
}"#;

impl<'a, H: dispatch::OkxAccountUpdateHandler> Core<'a, H> {
    fn json_to_payload(json: &Value) -> Result<EgressPayload, String> {
        let op = json
            .get("op")
            .and_then(|op| op.as_str())
            .ok_or(format!("Could not determine operation from JSON: {}", json))?;
        let api = match op {
            "order" => OkxExecuteApi::PlaceOrder,
            "amend-order" => OkxExecuteApi::ModifyOrder,
            "cancel-order" => OkxExecuteApi::CancelOrder,
            _ => return Err(format!("Unsupported operation: {}", op)),
        };

        Ok(EgressPayload {
            api,
            payload: Bytes::from(json.to_string()),
        })
    }

    fn handle_user_cmd(cmd: String) -> Result<EgressPayload, String> {
        match cmd.as_str() {
            "/sample:order-list" => Ok(EgressPayload {
                api: OkxExecuteApi::OrderList,
                payload: Bytes::from_static(&b"/api/v5/trade/orders-pending"[..]),
            }),
            "/sample:place-order" => Ok(EgressPayload {
                api: OkxExecuteApi::PlaceOrder,
                payload: Bytes::from_static(PLACE_ORDER_SAMPLE.as_bytes()),
            }),
            "/sample:cancel-order" => Ok(EgressPayload {
                api: OkxExecuteApi::PlaceOrder,
                payload: Bytes::from_static(CANCEL_ORDER_SAMPLE.as_bytes()),
            }),
            "/sample:modify-order" => Ok(EgressPayload {
                api: OkxExecuteApi::PlaceOrder,
                payload: Bytes::from_static(MODIFY_ORDER_SAMPLE.as_bytes()),
            }),
            cmd => {
                let maybe_json = serde_json::from_str::<Value>(cmd);
                match maybe_json {
                    Ok(json) => Self::json_to_payload(&json),
                    Err(e) => {
                        tracing::debug!(
                            ?e,
                            "JSON not detected, falling back to OrderList endpoint"
                        );
                        Ok(EgressPayload {
                            api: OkxExecuteApi::OrderList,
                            payload: Bytes::from(cmd.to_owned()),
                        })
                    }
                }
            }
        }
    }

    pub async fn start(mut self) {
        let (tx, rx) = (self.user_output, self.user_commands);

        let egress = self.tx.clone();
        tokio::task::spawn(async move {
            while let Ok(msg) = rx.recv().await {
                match Self::handle_user_cmd(msg) {
                    Ok(msg) => {
                        if let Err(_) = egress.send(msg).await {
                            tracing::error!("failed to send user command to egress");
                        }
                    }
                    Err(e) => {
                        tracing::error!(?e, "user command error");
                        if let Err(_) = tx.send(e).await {
                            tracing::debug!("failed to send error message to user output");
                        }
                    }
                }
            }
        });

        loop {
            let msg = self
                .rx
                .recv()
                .await
                .expect("always able to receive from channel");

            match msg {
                CorePayload::Message(bytes) => {
                    if let Err(err) = self.dispatcher.dispatch(&bytes) {
                        tracing::error!(?err, "failed dispatching message");
                        return;
                    }

                    let data = std::str::from_utf8(&bytes).unwrap();
                    tracing::info!(data, "msg received");
                }
                CorePayload::IngressError(err) => {
                    tracing::error!(?err, "received error from ingress");
                }
                CorePayload::EgressError(err) => {
                    tracing::error!(?err, "received error from ingress");
                }
            }
        }
    }

    pub async fn initialize(
        rx: kanal::AsyncReceiver<CorePayload>,
        egress: kanal::AsyncSender<EgressPayload>,
        user_commands: kanal::AsyncReceiver<String>,
        user_output: kanal::AsyncSender<String>,
        handler: &'a mut H,
    ) -> Self {
        Self {
            rx,
            tx: egress,
            user_commands,
            user_output,
            dispatcher: Dispatcher::new(handler),
        }
    }
}
