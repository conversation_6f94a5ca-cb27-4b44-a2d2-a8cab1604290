[package]
name = "straylight-common"
version.workspace = true
edition.workspace = true

[dependencies]
base64 = "0.22.1"
bytes = "1.10.1"
futures.workspace = true
hmac-sha256 = "1.1.12"
parking_lot = { version = "0.12.3", features = ["arc_lock"] }
secrecy.workspace = true
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
thiserror = "2.0.12"
tokio.workspace = true
tokio-tungstenite.workspace = true
tracing.workspace = true
tungstenite = "0.26.2"

[dev-dependencies]
tokio = { workspace = true, features = ["rt", "macros"] }
