use secrecy::{ExposeS<PERSON><PERSON>, SecretString};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct LoginDetails {
    pub api_key: SecretString,
    pub passphrase: SecretString,
    pub secret_key: SecretString,
}

impl LoginDetails {
    /// Compute the base64-encode signature string for the given payload
    pub fn signature(&self, method: &str, request_path: &str, timestamp: &str) -> String {
        let mut hmac = hmac_sha256::HMAC::new(self.secret_key.expose_secret().as_bytes());
        hmac.update(timestamp);
        hmac.update(method.as_bytes());
        hmac.update(request_path.as_bytes());
        let sig = hmac.finalize();

        use base64::Engine;
        let engine = base64::engine::general_purpose::STANDARD;
        engine.encode(sig)
    }
}
