use bytes::Bytes;
use thiserror::Error;

use super::OkxExecuteApi;

/// Represents a message sent to the egress
#[derive(Debug)]
pub struct EgressPayload {
    pub api: OkxExecuteApi,
    pub payload: Bytes,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub enum RestFailure {
    #[error("failed to create request with given payload: {0}")]
    BadPayload(String),
    #[error("failed to send request: {0}")]
    SendFailed(String),
    #[error("failed to receive response body: {0}")]
    FailedResponse(String),
}

#[derive(Debug, <PERSON>lone, Error)]
pub enum EgressError {
    #[error("payload was not valid utf-8")]
    NonUtf8Payload,
    #[error("failed when sending a WS message to the server: {0}")]
    SendWSFailed(String),
    #[error("failed when sending a REST message to the server: {0}")]
    SendRestFailed(RestFailure),
    #[error("no websocket connection available")]
    NoConnection,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub enum EgressInitError {
    #[error("failed to initialize HTTP client: {0}")]
    FailedClientInit(String),
}
