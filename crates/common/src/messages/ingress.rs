use bytes::Bytes;
use thiserror::Error;

use super::{EgressError, OkxExecuteApi};

/// Represents a message sent to the ingress
pub enum IngressPayload {
    MessageSent {
        api: OkxExecuteApi,
        response: Option<Bytes>,
    },
    Error(EgressError),
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>rror)]
pub enum LoginError {
    #[error("failed to send login request: {0}")]
    RequestFailed(String),
    #[error("no response received to login request")]
    NoResponse,
    #[error("failed deserializing login response: {inner}; response='{raw_message}'")]
    InvalidResponse { inner: String, raw_message: String },
    #[error("received unexpected type of response")]
    UnexpectedResponse,
    #[error("failed to retrieve login response: {0}")]
    ReceiveFailed(String),
    #[error("failed to login: {msg}")]
    Failed {
        code: String,
        msg: String,
        conn_id: String,
    },
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub enum SubscriptionError {
    #[error("failed to send subscription message: {0}")]
    SendFailure(String),
    #[error("failed to flush subscription messages: {0}")]
    FlushFailure(String),

    #[error("no response received to subscription request")]
    NoResponse,
    #[error("failed deserializing subscription response: {0}")]
    InvalidResponse(String),
    #[error("received unexpected type of response")]
    UnexpectedResponse,

    #[error("failed to retrieve subscription response: {0}")]
    ReceiveFailed(String),

    #[error("subscription successful but unable to retrieve channel: {0}")]
    UnknownChannel(String),

    #[error("subscription successful but channel unknown: {0}")]
    UnsupportedChannel(String),

    #[error("subscription successful for unexpected subscription: {0}")]
    UnexpectedSubscription(String),

    #[error("failed to subscribe: {msg}")]
    Failed {
        code: String,
        msg: String,
        conn_id: String,
    },
}

#[derive(Debug, Clone, Error)]
pub enum IngressError {
    #[error("failed to connect to websocket: {0}")]
    ConnectionFailed(String),
    #[error("failed while receiving a message from the server: {0}")]
    FailedReceiving(String),
    #[error("unexpected message from server")]
    UnexpectedMessage,
    #[error("remote server issued disconnection: {reason:?}")]
    ServerDisconnected { reason: Option<String> },
    #[error("egress shut down response channel")]
    EgressDisconnected,
    #[error("error occurred during login: {0}")]
    Login(LoginError),
    #[error("error occurred during subscription: {0}")]
    Subscribe(SubscriptionError),
}
