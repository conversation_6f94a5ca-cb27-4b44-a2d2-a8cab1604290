use futures::stream::SplitSink;
use tokio::net::TcpStream;
use tokio_tungstenite::{MaybeTlsStream, WebSocketStream};
use tungstenite::Message;

pub type Websocket = WebSocketStream<MaybeTlsStream<TcpStream>>;

/// The item sent across from the ingress to the egress
///
/// Where the egress will send data
pub type EgressTx = SplitSink<Websocket, Message>;

/// Contains common message definitions
pub mod messages;

/// Contains definitions for login interactions
pub mod login;

pub use login::LoginDetails;
