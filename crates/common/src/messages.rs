use serde::Deserialize;

/// Represents an error response from the exchange
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OkxError {
    pub code: String,
    pub msg: String,
    pub conn_id: String,
}

/// Indicates the type of API to use
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>)]
pub enum OkxExecuteApi {
    PlaceOrder,
    CancelOrder,
    ModifyOrder,
    OrderList,
}

mod ingress;
pub use ingress::*;

mod core;
pub use core::*;

mod egress;
pub use egress::*;
