use bytes::Bytes;
use futures::SinkExt;
use isahc::AsyncReadResponseExt;
use tungstenite::Message;

use straylight_common::{
    EgressTx, LoginDetails,
    messages::{
        CorePayload, EgressError, EgressInitError, EgressPayload, OkxExecuteApi,
        RestFailure as Error,
    },
};

mod rest;
use rest::RestFactory;

pub struct Egress {
    connections: kanal::AsyncReceiver<EgressTx>,
    factory: RestFactory,
    tx: Option<EgressTx>,
    client: isahc::HttpClient,
}

impl Egress {
    async fn poll_for_connection(&mut self) -> Result<(), EgressError> {
        match self
            .connections
            .try_recv()
            .expect("always able to receive from ingress")
        {
            Some(conn) => {
                self.tx.replace(conn);
                Ok(())
            }
            None => {
                if self.tx.is_none() {
                    Err(EgressError::NoConnection)
                } else {
                    Ok(())
                }
            }
        }
    }

    async fn send_rest(
        &self,
        api: OkxExecuteApi,
        payload: Bytes,
        core: &kanal::AsyncSender<CorePayload>,
    ) -> Result<(), Error> {
        let req = match self.factory.prepare_request(payload) {
            Ok(req) => req,
            Err(err) => {
                tracing::warn!(?err, "invalid payload, unable to create request");
                return Err(Error::BadPayload(err.to_string()));
            }
        };

        let mut response = match self.client.send_async(req).await {
            Ok(response) => response,
            Err(err) => {
                tracing::warn!(?err, "failed to send request");
                return Err(Error::SendFailed(err.to_string()));
            }
        };

        let core = core.clone();
        // forward response to core
        tokio::task::spawn(async move {
            let body = match response.bytes().await {
                Ok(bytes) => bytes,
                Err(err) => {
                    tracing::warn!(?err, "failed to receive response body");
                    let error_payload = CorePayload::EgressError(EgressError::SendRestFailed(
                        Error::FailedResponse(err.to_string()),
                    ));
                    if let Err(_) = core.send(error_payload).await {
                        tracing::error!("failed to notify core of REST response body failure");
                        tracing::debug!(?err, "original REST error that was not forwarded");
                    }
                    return;
                }
            };

            let success_payload = CorePayload::Message(body.into());
            if let Err(_) = core.send(success_payload).await {
                tracing::error!("failed to notify core of sent REST message");
            }
        });
        Ok(())
    }

    /// Handle an outgoing message
    pub async fn handle_msg(
        &mut self,
        message: &EgressPayload,
        core: &kanal::AsyncSender<CorePayload>,
    ) {
        if let Err(err) = self.poll_for_connection().await {
            let error_payload = CorePayload::EgressError(err);
            if let Err(_) = core.send(error_payload).await {
                tracing::error!("failed to notify core of missing connection");
            }
            return;
        }
        let tx = self.tx.as_mut().unwrap();
        let payload = message.payload.clone();

        match message.api {
            OkxExecuteApi::PlaceOrder | OkxExecuteApi::CancelOrder | OkxExecuteApi::ModifyOrder => {
                let Ok(payload) = payload.try_into() else {
                    let error_payload = CorePayload::EgressError(EgressError::NonUtf8Payload);
                    if let Err(_) = core.send(error_payload).await {
                        tracing::error!("failed to notify core of non-UTF8 payload error");
                    }
                    return;
                };

                if let Err(e) = tx.send(Message::Text(payload)).await {
                    let error_payload =
                        CorePayload::EgressError(EgressError::SendWSFailed(e.to_string()));
                    if let Err(_) = core.send(error_payload).await {
                        tracing::error!("failed to notify core of WebSocket send failure");
                    }
                    return;
                }
            }
            OkxExecuteApi::OrderList => {
                if let Err(err) = self.send_rest(message.api, payload, core).await {
                    let error_payload =
                        CorePayload::EgressError(EgressError::SendRestFailed(err.clone()));
                    if let Err(_) = core.send(error_payload).await {
                        tracing::error!("failed to notify core of REST send failure");
                        tracing::debug!(?err, "original REST error that was not forwarded");
                    }
                }
                return;
            }
        }
    }

    /// Initialize an Egress service
    ///
    /// Args:
    ///
    /// * `connections`: the kanal channel to retrieve websocket connections from the Ingress
    /// * `login_details`: the login credentials for OKX API authentication
    /// * `core_tx`: the channel to send responses directly to the core service
    pub async fn initialize(
        connections: kanal::AsyncReceiver<EgressTx>,
        login_details: LoginDetails,
    ) -> Result<Self, EgressInitError> {
        let mut factory = RestFactory::new("https://www.okx.com");
        factory.login_with(login_details);

        let client = isahc::HttpClient::new()
            .map_err(|e| EgressInitError::FailedClientInit(e.to_string()))?;

        Ok(Self {
            connections,
            factory,
            tx: None,
            client,
        })
    }
}
