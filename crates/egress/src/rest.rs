use bytes::Bytes;
use chrono::Utc;
use isahc::http::{
    self, Uri,
    request::{Builder, Request},
    uri,
};
use secrecy::ExposeSecret;

use straylight_common::LoginDetails;

pub struct RestFactory {
    /// Endpoint to forward requests to
    endpoint: Uri,

    /// Login details to use
    login: Option<LoginDetails>,
}

impl RestFactory {
    /// Create a new factory
    ///
    /// The factory will properly forward a given payload with the configured credentials
    pub fn new(endpoint: &str) -> Self {
        Self {
            endpoint: endpoint.parse::<Uri>().expect("valid endpoint"),
            login: None,
        }
    }

    /// Add login credentials to the factory
    pub fn login_with(&mut self, login_details: LoginDetails) -> &mut Self {
        self.login.replace(login_details);
        self
    }

    /// Populates a given request with the appropriate login headers
    ///
    /// Will fail if the request is missing the method, or if the uri is invalid or if the uri is missing a path and query parameters
    fn login(&self, mut req: Builder) -> Option<Builder> {
        /*
         * All private REST requests must contain the following headers:
        OK-ACCESS-KEY The API key as a String.
        OK-ACCESS-SIGN The Base64-encoded signature (see Signing Messages subsection for details).
        OK-ACCESS-TIMESTAMP The UTC timestamp of your request .e.g : 2020-12-08T09:08:57.715Z
        OK-ACCESS-PASSPHRASE The passphrase you specified when creating the API key.
         */
        if let Some(login) = &self.login {
            let method = req.method_ref()?.as_str();

            let request_path = {
                let uri = req.uri_ref()?;
                let path = uri.path_and_query()?;

                path.as_str()
            };

            let timestamp = Utc::now().to_rfc3339_opts(chrono::SecondsFormat::Millis, true);
            let sig = login.signature(method, request_path, &timestamp);

            req = req
                .header("OK-ACCES-SIGN", sig)
                .header("OK-ACCESS-KEY", login.api_key.expose_secret())
                .header("OK-ACCESS-TIMESTAMP", timestamp)
                .header("OK-ACCESS-PASSPHRASE", login.passphrase.expose_secret());
        }

        Some(req)
    }

    /// Create a new [`Request`] with the configured payload and credentials
    pub fn prepare_request(
        &self,
        path_and_query: Bytes,
    ) -> Result<Request<&'static str>, http::Error> {
        let path_and_query = uri::PathAndQuery::from_maybe_shared(path_and_query)?;

        let mut parts = self.endpoint.clone().into_parts();
        parts.path_and_query.replace(path_and_query);

        // original uri is ok, path_and_query checked in the conversion
        let uri = Uri::from_parts(parts).ok().unwrap();

        let base_req = Builder::new()
            .uri(uri)
            .method("GET")
            .header("Content-Type", "application/json");

        // method, uri, path and query params provided
        self.login(base_req).unwrap().body("{}")
    }
}
