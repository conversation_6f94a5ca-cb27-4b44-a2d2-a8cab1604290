[package]
name = "straylight-egress"
version.workspace = true
edition.workspace = true

[dependencies]
bytes = "1.10.1"
chrono = { version = "0.4.41", features = ["now"] }
futures.workspace = true
http = "1.3.1"
isahc = "1.7.2"
kanal.workspace = true
secrecy.workspace = true
straylight-common.workspace = true
tokio = { workspace = true, features = ["rt"] }
tracing.workspace = true
tungstenite = "0.26.2"
