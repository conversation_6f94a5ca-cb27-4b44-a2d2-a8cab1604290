use futures::{StreamExt, TryStreamExt};
use tokio_util::sync::PollSender;
use tungstenite::{Message, client::IntoClientRequest};

use straylight_common::{
    EgressTx, LoginDetails, Websocket,
    messages::{CorePayload, IngressError},
};

mod ws;
use ws::{SubscriptionRequest, WsFactory};

pub struct Ingress {
    connections: tokio::sync::mpsc::Receiver<Websocket>,

    to_egress: kanal::AsyncSender<EgressTx>,

    to_core: kanal::AsyncSender<CorePayload>,
}

enum Control {
    Continue,
    Reconnect,
    Break,
}

impl Ingress {
    async fn handle_ws_msg(
        tx: &kanal::AsyncSender<CorePayload>,
        msg: Result<Option<Message>, tungstenite::Error>,
    ) -> Control {
        match msg {
            // TODO: accept Message::Binary as well?
            Ok(Some(Message::Text(msg))) => {
                // TODO: verify valid json?
                // TODO: extract json payload?
                if let Err(_) = tx.send(CorePayload::Message(msg.into())).await {
                    tracing::error!("failed to send WebSocket message to core");
                }
                Control::Continue
            }
            Ok(Some(Message::Close(reason))) => {
                tracing::warn!(?reason, "server closed connection");
                if let Err(_) = tx
                    .send(CorePayload::IngressError(
                        IngressError::ServerDisconnected {
                            reason: reason.map(|c| c.to_string()),
                        },
                    ))
                    .await
                {
                    tracing::error!("failed to notify core of server disconnect");
                }
                Control::Reconnect
            }
            // TODO: propagate unexpected message?
            Ok(Some(msg)) => {
                tracing::warn!(?msg, "unexpected message from server");
                if let Err(_) = tx
                    .send(CorePayload::IngressError(IngressError::UnexpectedMessage))
                    .await
                {
                    tracing::error!("failed to notify core of unexpected message");
                }
                Control::Continue
            }
            Ok(None)
            | Err(tungstenite::Error::ConnectionClosed | tungstenite::Error::AlreadyClosed) => {
                tracing::warn!("unable to receive further messages");
                if let Err(_) = tx
                    .send(CorePayload::IngressError(
                        IngressError::ServerDisconnected { reason: None },
                    ))
                    .await
                {
                    tracing::error!("failed to notify core of connection closure");
                }
                Control::Reconnect
            }
            Err(err) => {
                tracing::error!(?err, "error occurred while receiving message");
                if let Err(_) = tx
                    .send(CorePayload::IngressError(IngressError::FailedReceiving(
                        err.to_string(),
                    )))
                    .await
                {
                    tracing::error!("failed to notify core of receive error");
                }
                Control::Reconnect
            }
        }
    }

    /// Start the Ingress service
    ///
    /// This future is not meant to ever finish
    pub async fn start(mut self) {
        'connect: loop {
            let conn = self
                .connections
                .recv()
                .await
                .expect("always a connection ready");

            let (tx, rx) = conn.split();

            // forward tx part to egress
            self.to_egress
                .send(tx)
                .await
                .expect("always able to send to egresss");

            futures::pin_mut!(rx);

            loop {
                let msg = rx.try_next().await;

                match Self::handle_ws_msg(&self.to_core, msg).await {
                    Control::Reconnect => continue 'connect,
                    Control::Break => break 'connect,
                    _ => {}
                }
            }
        }
    }

    /// Initialize an Ingress service
    ///
    /// Args:
    ///
    /// * `core`: the kanal channel to send messages to the core
    ///
    /// * `egress_signal`: the kanal channel where the Egress will push expected responses for the Ingress
    ///
    /// * `egress_connection`: the kanal channel where the Ingress will push new websocket connections for the Egress to use
    ///
    /// * `login_details`: the login credentials for OKX API authentication (supports multiple identities)
    pub async fn initialize(
        core: kanal::AsyncSender<CorePayload>,
        egress_connection: kanal::AsyncSender<EgressTx>,
        login_details: Vec<LoginDetails>,
    ) -> Self {
        let connections = {
            let mut factory = WsFactory::new(
                "wss://ws.okx.com:8443/ws/v5/private"
                    .into_client_request()
                    .unwrap(),
            );

            for credentials in login_details {
                factory.login_with(credentials);
            }

            factory
                .errors_to(core.clone())
                .set_retries(3)
                .subscribe_to(SubscriptionRequest::account(None, None))
                .subscribe_to(SubscriptionRequest::balance_and_position())
                .subscribe_to(SubscriptionRequest::orders("ANY", None, None))
                .subscribe_to(SubscriptionRequest::fills(None));

            let stream = factory.into_stream();

            // wrap the stream behind a channel to make it easier to pass around
            let (tx, rx) = tokio::sync::mpsc::channel(1);
            let tx = PollSender::new(tx);

            tokio::task::spawn(stream.map(|conn| Ok(conn)).forward(tx));
            rx
        };

        Self {
            connections,
            to_egress: egress_connection,
            to_core: core,
        }
    }
}
