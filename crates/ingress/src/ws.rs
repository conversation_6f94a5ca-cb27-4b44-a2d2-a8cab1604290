use std::{
    fmt::Display,
    time::{SystemTime, UNIX_EPOCH},
};

use futures::{Stream, StreamExt, sink::SinkExt, stream::FusedStream};
use secrecy::ExposeSecret;
use serde_json::{Value, json};
use tokio_tungstenite::connect_async;
use tungstenite::{Message, handshake::client::Request};

use straylight_common::{
    LoginDetails, Websocket,
    messages::{CorePayload, IngressError, LoginError, OkxError, SubscriptionError},
};

mod messages;
use messages::{LoginResponse, SubscriptionResponse};

/// Represents a subscription channel
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum Channel {
    Account,
    BalanceAndPosition,
    Orders,
    Fills,
}

impl Channel {
    /// Retrieve the corresponding subscription name for the given [`Channel`]
    pub const fn subscription_name(&self) -> &'static str {
        match self {
            Self::Account => "account",
            Self::BalanceAndPosition => "balance_and_position",
            Self::Orders => "orders",
            Self::Fills => "fills",
        }
    }

    /// Attempt to match the given subscription name to a [`Channel`]
    pub fn from_subscription_name(from: &str) -> Option<Self> {
        match from {
            "account" => Some(Self::Account),
            "balance_and_position" => Some(Self::BalanceAndPosition),
            "orders" => Some(Self::Orders),
            "fills" => Some(Self::Fills),
            _ => None,
        }
    }
}

impl Display for Channel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.write_str(self.subscription_name())
    }
}

/// Represents a channel subscription request
#[derive(Debug, Clone)]
pub struct SubscriptionRequest {
    /// The channel to subscribe to
    channel: Channel,
    /// Arguments to the channel
    ///
    /// Please refer to the official documentation to know what this value should have
    args: Value,
}

impl SubscriptionRequest {
    /// Create a subscription request for `account` channel
    pub fn account(currency: Option<&str>, update_interval: Option<usize>) -> Self {
        let mut args = json!([{}]);
        if let Some(ccy) = currency {
            args[0]["ccy"] = ccy.into();
        }

        if let Some(up) = update_interval {
            args[0]["extraParams"]["updateInterval"] = up.into();
        }

        Self {
            channel: Channel::Account,
            args,
        }
    }

    /// Create a subscription request for `balance_and_position` channel
    pub fn balance_and_position() -> Self {
        let args = json!([{}]);

        Self {
            channel: Channel::BalanceAndPosition,
            args,
        }
    }

    /// Create a subscription request for `orders` channel
    pub fn orders(inst_type: &str, inst_family: Option<&str>, inst_id: Option<&str>) -> Self {
        let mut args = json!([{
            "instType": inst_type,
        }]);

        if let Some(family) = inst_family {
            args[0]["instFamily"] = family.into();
        }

        if let Some(id) = inst_id {
            args[0]["instId"] = id.into();
        }

        Self {
            channel: Channel::Orders,
            args,
        }
    }

    /// Create a subscription request for `fills` channel
    pub fn fills(inst_id: Option<&str>) -> Self {
        let mut args = json!([{}]);

        if let Some(id) = inst_id {
            args[0]["instId"] = id.into();
        }

        Self {
            channel: Channel::Fills,
            args,
        }
    }
}

pub struct WsFactory {
    /// Request to initialize websocket connection
    request: Request,

    /// Subscription messages
    subscriptions: Vec<SubscriptionRequest>,

    /// Login details
    logins: Vec<LoginDetails>,

    /// Send errors to this channel
    errors: kanal::AsyncSender<CorePayload>,

    /// Number of retries to attempt for a given request
    ///
    /// None: unlimited retries
    retries: Option<usize>,
}

impl WsFactory {
    /// Create a new factory
    ///
    /// The factory will create a new websocket connection with the configured request
    /// and send the configured subscription requests
    pub fn new(new_conn_request: Request) -> Self {
        Self {
            request: new_conn_request,
            subscriptions: vec![],
            logins: vec![],
            errors: kanal::bounded_async(0).0,
            retries: None,
        }
    }

    /// Sets the number of retries to attempt for a given request
    ///
    /// Pass `None` to allow infinite retries
    pub fn set_retries(&mut self, retries: impl Into<Option<usize>>) -> &mut Self {
        self.retries = retries.into();
        self
    }

    /// Set channel to proapgate errors to
    pub fn errors_to(&mut self, tx: kanal::AsyncSender<CorePayload>) -> &mut Self {
        self.errors = tx;
        self
    }

    /// Add login credentials to the factory
    pub fn login_with(&mut self, login_details: LoginDetails) -> &mut Self {
        self.logins.push(login_details);
        self
    }

    /// Add a subscription request to the factory
    pub fn subscribe_to(&mut self, sub: SubscriptionRequest) -> &mut Self {
        // TODO: ok to have multiple subs?
        // maybe just use a set
        match self
            .subscriptions
            .iter_mut()
            .find(|req| sub.channel == req.channel)
        {
            Some(req) => req.args = sub.args,
            None => self.subscriptions.push(sub),
        }

        self
    }

    /// Attempts to login with the configured credentials
    ///
    /// In case of failure, no connection is returned
    async fn login(&self, mut conn: Websocket) -> Result<Websocket, LoginError> {
        let mut logins = vec![];

        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards")
            .as_secs()
            .to_string();

        // prepare login requests for all configured credentials
        for login in self.logins.clone().into_iter() {
            let payload = json!({
                "apiKey": login.api_key.expose_secret(),
                "passphrase": login.passphrase.expose_secret(),
                "timestamp": timestamp,
                "sign": login.signature("GET", "/users/self/verify", &timestamp)
            });
            logins.push(payload);
        }

        // early return if we don't have any logins
        if logins.is_empty() {
            return Ok(conn);
        }

        let req = json!({"op": "login", "args": logins });
        if let Err(e) = conn.send(Message::text(req.to_string())).await {
            tracing::warn!(err = ?e, "failed to send login request");
            return Err(LoginError::RequestFailed(e.to_string()));
        }

        // check login result
        let mut responses = conn.take(self.logins.len());
        loop {
            let next = responses.next().await;
            if next.is_none() && !responses.is_terminated() {
                tracing::warn!("login response not received");
                return Err(LoginError::NoResponse);
            } else if next.is_none() {
                // we are not expecting any more responses
                break;
            }

            let response: LoginResponse = match next.unwrap() {
                Ok(Message::Text(msg)) => {
                    let msg = msg.as_str();
                    match serde_json::from_str(msg) {
                        Ok(response) => response,
                        Err(err) => {
                            tracing::warn!(
                                response = msg,
                                ?err,
                                "failed to deserialize subscription response"
                            );
                            return Err(LoginError::InvalidResponse {
                                inner: err.to_string(),
                                raw_message: msg.to_string(),
                            });
                        }
                    }
                }
                Ok(msg) => {
                    tracing::warn!(?msg, "unexpected subscription response!");
                    return Err(LoginError::UnexpectedResponse);
                }
                Err(err) => {
                    tracing::warn!(?err, "failed to retrieve subscription response!");
                    return Err(LoginError::ReceiveFailed(err.to_string()));
                }
            };

            match response {
                LoginResponse::Error(OkxError { code, msg, conn_id }) => {
                    // TODO: all login or any is fine?
                    return Err(LoginError::Failed { code, msg, conn_id });
                }
                LoginResponse::Login { conn_id } => {
                    // TODO: determine which credentials logged in?
                    tracing::info!(conn_id, "login succesfull");
                }
            }
        }

        // retrieve the inner stream from `Take`
        Ok(responses.into_inner())
    }

    /// Retrieve a new websocket connection ready to be used
    ///
    /// Will attempt to subscribe as necessary
    pub async fn get_connection(&self) -> Websocket {
        let mut retries = self.retries.clone();
        'outer: loop {
            match &mut retries {
                Some(0) => {
                    tracing::error!("connection failed after configured number of retries!");
                    // if we panic here the app will just hang
                    // as the tokio runtime by default just continues on the other tasks
                    // we wait to allow any remaining processing (like printing the last error message)
                    tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                    std::process::exit(1);
                }
                Some(num) => {
                    *num -= 1;
                }
                _ => {}
            }

            let Ok((websocket, _)) = connect_async(self.request.clone()).await else {
                tracing::warn!(request = ?self.request, "failed to establish websocket connection, retrying...");
                if let Err(_) = self
                    .errors
                    .send(CorePayload::IngressError(IngressError::ConnectionFailed(
                        self.request.uri().to_string(),
                    )))
                    .await
                {
                    tracing::error!("failed to notify core of connection failure");
                }
                continue;
            };

            // login before subscribing
            let mut websocket = match self.login(websocket).await {
                Ok(ws) => ws,
                Err(err) => {
                    tracing::warn!("failed to login, retrying...");
                    if let Err(_) = self
                        .errors
                        .send(CorePayload::IngressError(IngressError::Login(err)))
                        .await
                    {
                        tracing::error!("failed to notify core of login failure");
                    }
                    continue;
                }
            };

            for mut sub in self.subscriptions.clone().into_iter() {
                // make sure the subscription request has the indicated channel
                sub.args[0]["channel"] = sub.channel.subscription_name().into();

                if let Err(err) = websocket
                    .feed(tungstenite::Message::text(
                        json!({
                            "op": "subscribe",
                            "args": sub.args,
                        })
                        .to_string(),
                    ))
                    .await
                {
                    tracing::warn!(channel = %sub.channel, ?err, "failed to send subscription message");
                    if let Err(_) = self
                        .errors
                        .send(CorePayload::IngressError(IngressError::Subscribe(
                            SubscriptionError::SendFailure(err.to_string()),
                        )))
                        .await
                    {
                        tracing::error!("failed to notify core of subscription send failure");
                    }
                    continue 'outer;
                }
            }

            if let Err(err) = websocket.flush().await {
                tracing::warn!(?err, "unable to flush subscription messages");
                if let Err(_) = self
                    .errors
                    .send(CorePayload::IngressError(IngressError::Subscribe(
                        SubscriptionError::FlushFailure(err.to_string()),
                    )))
                    .await
                {
                    tracing::error!("failed to notify core of subscription flush failure");
                }
                continue;
            }

            // check subscriptions were successful
            let mut subscriptions = self
                .subscriptions
                .iter()
                .map(|req| req.channel)
                .collect::<Vec<_>>();
            let mut responses = websocket.take(subscriptions.len());
            loop {
                let next = responses.next().await;
                if next.is_none() && !responses.is_terminated() {
                    tracing::warn!("subscription response not received");
                    if let Err(_) = self
                        .errors
                        .send(CorePayload::IngressError(IngressError::Subscribe(
                            SubscriptionError::NoResponse,
                        )))
                        .await
                    {
                        tracing::error!("failed to notify core of missing subscription response");
                    }
                    continue 'outer;
                } else if next.is_none() {
                    // we are not expecting any more responses
                    break;
                }

                let response: SubscriptionResponse = match next.unwrap() {
                    Ok(Message::Text(msg)) => {
                        let msg = msg.as_str();
                        match serde_json::from_str(msg) {
                            Ok(response) => response,
                            Err(err) => {
                                tracing::warn!(
                                    response = msg,
                                    ?err,
                                    "failed to deserialize subscription response"
                                );

                                if let Err(_) = self
                                    .errors
                                    .send(CorePayload::IngressError(IngressError::Subscribe(
                                        SubscriptionError::InvalidResponse(err.to_string()),
                                    )))
                                    .await
                                {
                                    tracing::error!(
                                        "failed to notify core of invalid subscription response"
                                    );
                                }
                                continue 'outer;
                            }
                        }
                    }
                    Ok(msg) => {
                        tracing::warn!(?msg, "unexpected subscription response!");
                        if let Err(_) = self
                            .errors
                            .send(CorePayload::IngressError(IngressError::Subscribe(
                                SubscriptionError::UnexpectedResponse,
                            )))
                            .await
                        {
                            tracing::error!(
                                "failed to notify core of unexpected subscription response"
                            );
                        }
                        continue 'outer;
                    }
                    Err(err) => {
                        tracing::warn!(?err, "failed to retrieve subscription response!");
                        if let Err(_) = self
                            .errors
                            .send(CorePayload::IngressError(IngressError::Subscribe(
                                SubscriptionError::ReceiveFailed(err.to_string()),
                            )))
                            .await
                        {
                            tracing::error!(
                                "failed to notify core of subscription receive failure"
                            );
                        }
                        continue 'outer;
                    }
                };

                // check response
                match response {
                    SubscriptionResponse::Error(OkxError { code, msg, conn_id }) => {
                        tracing::error!(code, msg, conn_id, "failed to subscribe");
                        if let Err(_) = self
                            .errors
                            .send(CorePayload::IngressError(IngressError::Subscribe(
                                SubscriptionError::Failed { code, msg, conn_id },
                            )))
                            .await
                        {
                            tracing::error!("failed to notify core of reported subscription error");
                        }
                        continue 'outer;
                    }
                    SubscriptionResponse::Subscribe { arg, conn_id } => {
                        // remove response from list of subscriptions submitted
                        let Some(Value::String(chan_name)) = arg.get("channel") else {
                            tracing::warn!(response = ?arg, conn_id, "subscription succesful but unable to retrieve channel!");
                            if let Err(_) = self
                                .errors
                                .send(CorePayload::IngressError(IngressError::Subscribe(
                                    SubscriptionError::UnknownChannel(arg.to_string()),
                                )))
                                .await
                            {
                                tracing::error!(
                                    "failed to notify core of unknown subscription channel"
                                );
                            }
                            continue 'outer;
                        };
                        let Some(channel) = Channel::from_subscription_name(&chan_name) else {
                            tracing::warn!(response = ?arg, channel = chan_name, conn_id, "subscription succesful but unknoen channel!");
                            if let Err(_) = self
                                .errors
                                .send(CorePayload::IngressError(IngressError::Subscribe(
                                    SubscriptionError::UnsupportedChannel(chan_name.to_string()),
                                )))
                                .await
                            {
                                tracing::error!(
                                    "failed to notify core of unsupported subscription channel"
                                );
                            }
                            continue 'outer;
                        };

                        // TODO: maybe check arg as well?
                        // TODO: if we want to check arg, maybe reuse `SubscriptionRequest`
                        let Some((i, _)) = subscriptions
                            .iter_mut()
                            .enumerate()
                            .find(|(_, sub)| **sub == channel)
                        else {
                            tracing::warn!(
                                subscribed = channel.subscription_name(),
                                "received succesfull subscription response for unexpected subscription"
                            );

                            if let Err(_) = self
                                .errors
                                .send(CorePayload::IngressError(IngressError::Subscribe(
                                    SubscriptionError::UnexpectedSubscription(channel.to_string()),
                                )))
                                .await
                            {
                                tracing::error!(
                                    "failed to notify core of unexpected subscription response for channel"
                                );
                            }
                            continue 'outer;
                        };
                        subscriptions.swap_remove(i);

                        tracing::info!(
                            arg = arg.to_string(),
                            conn_id,
                            channel = channel.subscription_name(),
                            "subscribed succesfully"
                        );
                    }
                }
            }

            // retrieve the inner stream from `Take`
            return responses.into_inner();
        }
    }

    /// Converts the configured factory into a stream
    ///
    /// Each item will be a fully configured websocket
    pub fn into_stream(self) -> impl Stream<Item = Websocket> {
        async_stream::stream! {
            for _ in 0.. {
                yield self.get_connection().await;
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    use tungstenite::{Message, client::IntoClientRequest};
    use wiremocket::{Mock, MockServer};

    fn match_sub(channel: &str) -> impl Fn(&Message) -> Option<bool> {
        move |msg| {
            let json = match msg {
                Message::Text(t) => serde_json::from_str::<Value>(t.as_str()),
                Message::Binary(b) => serde_json::from_slice(b.as_ref()),
                // if the message is unexpected we fail the match
                _ => return Some(false),
            };
            // if not a json we fail the match
            let Ok(json) = json else {
                return Some(false);
            };

            // check for op: "subscribe" and args.channel: channel
            if json["op"].as_str() != Some("subscribe") {
                return Some(false);
            }

            if json["args"]["channel"].as_str() != Some(channel) {
                // not the right channel, ignore
                return None;
            }

            Some(true)
        }
    }

    fn subscription_responder(msg: Message) -> Message {
        let txt = msg.to_text().unwrap();
        let json = serde_json::from_str::<Value>(txt).unwrap();
        let channel = &json["args"]["channel"];

        Message::text(
            json!({
                "event": "subscribe",
                "connId": "foo-bar",
                "arg": {
                    "channel": channel
                }
            })
            .to_string(),
        )
    }

    #[tokio::test]
    async fn subscribe_one() {
        _ = tracing_subscriber::fmt::try_init();

        let server = MockServer::start().await;
        let request = server.uri().into_client_request().unwrap();

        let acct = Mock::given(match_sub(Channel::Account.subscription_name()))
            .one_to_one_response(subscription_responder)
            .expect(1..);

        server.register(acct).await;

        let mut factory = WsFactory::new(request);
        factory.subscribe_to(SubscriptionRequest::account(None, None));

        tokio::time::timeout(
            std::time::Duration::from_millis(200),
            factory.get_connection(),
        )
        .await
        .expect("connection failed");

        server.verify().await;
    }

    #[tokio::test]
    async fn subscribe_multi() {
        _ = tracing_subscriber::fmt::try_init();

        let server = MockServer::start().await;
        let request = server.uri().into_client_request().unwrap();

        let acct = Mock::given(match_sub(Channel::Account.subscription_name()))
            .add_matcher(match_sub(Channel::BalanceAndPosition.subscription_name()))
            .one_to_one_response(subscription_responder)
            .expect(1);
        let balops = Mock::given(match_sub(Channel::BalanceAndPosition.subscription_name()))
            .one_to_one_response(subscription_responder)
            .expect(1);

        server.register(acct).await;
        server.register(balops).await;

        let mut factory = WsFactory::new(request);
        factory.subscribe_to(SubscriptionRequest::balance_and_position());
        factory.subscribe_to(SubscriptionRequest::account(None, None));

        tokio::time::timeout(
            std::time::Duration::from_millis(200),
            factory.get_connection(),
        )
        .await
        .expect("connection failed");

        // FIXME: wasn't able to get both Mocks to match
        // server.verify().await;
    }

    #[tokio::test]
    async fn fail_to_connect() {
        _ = tracing_subscriber::fmt::try_init();

        let request = "wss://localhost:12345".into_client_request().unwrap();

        let mut factory = WsFactory::new(request);
        factory.subscribe_to(SubscriptionRequest::account(None, None));

        tokio::time::timeout(
            std::time::Duration::from_millis(200),
            factory.get_connection(),
        )
        .await
        .expect_err("connection succeeded");
    }
}
