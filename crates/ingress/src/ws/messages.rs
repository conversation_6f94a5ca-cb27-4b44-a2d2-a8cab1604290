use serde::Deserialize;
use serde_json::Value;

use straylight_common::messages::OkxError;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
#[serde(tag = "event")]
pub enum SubscriptionResponse {
    Error(OkxError),
    Subscribe { arg: Value, conn_id: String },
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
#[serde(tag = "event")]
pub enum LoginResponse {
    Error(OkxError),
    Login { conn_id: String },
}
