[package]
name = "straylight-ingress"
version.workspace = true
edition.workspace = true

[dependencies]
straylight-common.workspace = true

tracing.workspace = true

futures.workspace = true
tokio.workspace = true
tokio-tungstenite.workspace = true
async-stream = "0.3.6"
tokio-util = "0.7.15"
tungstenite = "0.26.2"

serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
hmac-sha256 = "1.1.12"
base64 = "0.22.1"
kanal.workspace = true
secrecy.workspace = true

[dev-dependencies]
tracing-subscriber = "0.3"
wiremocket = "0.2.0"
