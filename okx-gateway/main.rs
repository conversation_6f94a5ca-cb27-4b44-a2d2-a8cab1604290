use std::io::Write;

use futures::FutureExt;
use r3bl_tui::ReadlineEvent;
use tracing::Dispatch;

use straylight_core::Core;
use straylight_egress::Egress;
use straylight_ingress::Ingress;

fn main() {
    let runtime = tokio::runtime::Builder::new_multi_thread()
        .enable_all()
        .build()
        .expect("Failed building tokio runtime");

    // we use a temporary subscriber while we don't have the async prompt writer
    let tmp_subscriber = Dispatch::new(tracing_subscriber::fmt().finish());

    // setup a readline context for the async prompt
    let mut ctx = None;
    tracing::dispatcher::with_default(&tmp_subscriber, || {
        runtime.block_on(async {
            ctx = match r3bl_tui::ReadlineAsyncContext::try_new(Some("okx> ")).await {
                Err(err) => {
                    tracing::error!(?err, "failed to create terminal");
                    None
                }
                Ok(None) => {
                    tracing::warn!("terminal was not fully interactive");
                    None
                }
                Ok(Some(ctx)) => Some(ctx),
            };
        });
    });

    let Some(mut ctx) = ctx else {
        return;
    };

    // use the context output for logging
    let tracing_writer = ctx.clone_shared_writer();
    tracing_subscriber::fmt()
        .with_writer(move || tracing_writer.clone())
        .init();

    // channel from core to context for shutdown if needed
    let (shutdown_tx, shutdown_rx) = kanal::bounded_async::<()>(1);

    // setup a task to write to stdout for the user
    let mut output_writer = ctx.clone_shared_writer();
    let (output_tx, output_rx) = kanal::unbounded_async::<String>();

    let output_task = async move {
        while let Ok(msg) = output_rx.recv().await {
            _ = output_writer.write_all(msg.as_bytes());
            _ = output_writer.flush();
        }

        _ = shutdown_tx.send(()).await;
    };

    // channel for the core to receive input from
    let (input_tx, input_rx) = kanal::unbounded_async::<String>();

    // task to read user input
    let drive_task = async {
        let shutdown = shutdown_rx.recv().fuse();
        futures::pin_mut!(shutdown);

        loop {
            let read_line = async {
                match ctx.read_line().await {
                    Ok(input) => match input {
                        ReadlineEvent::Line(cmd) => {
                            if let Err(err) = input_tx.send(cmd).await {
                                tracing::error!(?err, "failed to send user input to core!");
                                None
                            } else {
                                Some(())
                            }
                        }
                        ReadlineEvent::Eof | ReadlineEvent::Interrupted => None,
                        _ => Some(()),
                    },
                    Err(err) => {
                        tracing::error!(?err, "failed reading user input!");
                        None
                    }
                }
            }
            .fuse();
            futures::pin_mut!(read_line);

            futures::select_biased! {
                _ = shutdown => break,
                control = read_line => if control.is_none() { break },
            }
        }

        // given errors or shutdown received from core
        _ = ctx.request_shutdown(Some("Shutting down...")).await;
        ctx.await_shutdown().await;
    };

    let straylight = gateway(output_tx, input_rx);
    runtime.spawn(output_task);
    runtime.spawn(straylight);

    runtime.block_on(drive_task);

    // we returned from the driver task, so we should be shutting down
    runtime.shutdown_background();
}

async fn gateway(core_tx: kanal::AsyncSender<String>, core_rx: kanal::AsyncReceiver<String>) {
    let (to_core_tx, to_core_rx) = kanal::unbounded_async();
    let (core_to_egress_tx, core_to_egress_rx) = kanal::unbounded_async();

    let (egress_conn_tx, egress_conn_rx) = kanal::unbounded_async();

    // Load login details from environment variables
    let egress_login = match load_okx_credentials() {
        Ok(credentials) => credentials,
        Err(err) => {
            tracing::error!(
                ?err,
                "Failed to load OKX credentials from environment variables"
            );
            return;
        }
    };
    let ingress_login_details = vec![egress_login.clone()];

    let mut handler = ();
    let core = Core::initialize(
        to_core_rx,
        core_to_egress_tx,
        core_rx,
        core_tx,
        &mut handler,
    );
    let ingress = Ingress::initialize(to_core_tx.clone(), egress_conn_tx, ingress_login_details);
    let egress = Egress::initialize(egress_conn_rx, egress_login);

    let (core, ingress, egress) = futures::join!(core, ingress, egress);

    let egress = match egress {
        Ok(egress) => egress,
        Err(err) => {
            tracing::error!(?err, "Failed to initialize Egress");
            return;
        }
    };

    // sample
    let drive_egress = async move {
        let mut egress = egress;
        loop {
            let msg = core_to_egress_rx
                .recv()
                .await
                .expect("able to receive message from the core");

            tracing::info!(?msg, "sending to egress...");

            egress.handle_msg(&msg, &to_core_tx).await;
        }
    };
    futures::pin_mut!(drive_egress);

    futures::join!(core.start(), ingress.start(), drive_egress);
}

/// Load OKX credentials from environment variables
fn load_okx_credentials() -> Result<straylight_common::LoginDetails, String> {
    let api_key = std::env::var("STRX_PROP_OKX_API_KEY")
        .map_err(|_| "Missing environment variable: STRX_PROP_OKX_API_KEY")?;

    let passphrase = std::env::var("STRX_PROP_OKX_PASSPHRASE")
        .map_err(|_| "Missing environment variable: STRX_PROP_OKX_PASSPHRASE")?;

    let secret_key = std::env::var("STRX_PROP_OKX_SECRET_KEY")
        .map_err(|_| "Missing environment variable: STRX_PROP_OKX_SECRET_KEY")?;

    Ok(straylight_common::LoginDetails {
        api_key: api_key.into(),
        passphrase: passphrase.into(),
        secret_key: secret_key.into(),
    })
}
