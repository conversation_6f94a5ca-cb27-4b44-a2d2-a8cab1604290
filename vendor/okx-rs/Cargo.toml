[package]
name = "okx-rs"
version = "0.1.1"
edition = "2021"
description = "Unofficial Rust Library for the OKX V5 API"
repository = "https://github.com/roytang121/okx-rs/"
license = "MIT OR Apache-2.0"
keywords = ["okx", "crypto", "exchange", "trading"]
authors = ["<PERSON> <<EMAIL>>"]
readme = "./README.md"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = { version = "1", features = ["backtrace"] }
thiserror = "1"
base64 = "0.13"
serde = { version = "1.0", features = ["derive"] }
serde_json = { version = "1.0", features = ["arbitrary_precision"] }
serde_qs = "0.10.1"
serde_with = "3.7.0"
url = "2"
sha2 = { version = "0.10", default-features = false }
digest = { version = "0.10", features = ["dev"] }
hmac = { version = "0.12.1", features = [] }
const_format = "0.2"
rust_decimal = { version = "1", features = ["macros", "serde", "serde-float", "serde-arbitrary-precision"] }

# optional
tokio = { version = "1.0.0", features = ["rt", "rt-multi-thread", "macros"], optional = true }
simd-json = { version = "0.13.4", features = ["runtime-detection", "known-key"], optional = true }
tungstenite = { version = "0.20.1", features = ["native-tls"], optional = true }
log = { version = "0.4.20", optional = true }
env_logger = { version = "0.10.0", features = [], optional = true }

# FIXME: to be removed
futures-util = "0.3.28"

# FIXME: should be feature-gated
reqwest = { version = "0.11.22", features = ["json", "blocking"] }
chrono = "0.4.38"

[dev-dependencies]
dotenv = { version = "0.15.0" }

[features]
default = ["dep:log"]
vip = []
simd = ["dep:simd-json"]
websocket = ["dep:tungstenite"]
example = ["dep:env_logger", "dep:tokio", "websocket"]
orderbook = []
