.env
/target
# Generated by Cargo
# will have compiled files and executables
**/target/

# Remove Cargo.lock from gitignore if creating an executable, leave it for libraries
# More information here https://doc.rust-lang.org/cargo/guide/cargo-toml-vs-cargo-lock.html
Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk

# Added by machine
/machine_writing
thirdparty/machine/machine_writing

#Added by cargo

/target

index.html
keys.txt

# vscode config/state
.vscode

# perf output files
**/perf.data.*
**/perf.data

*.swp

.idea

env/data
certs

tmp_data

*.iml

.DS_Store

vendor
profile*.json