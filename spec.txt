OKX Order Gateway

The goal of this project is to build an application that facilitates the execution of orders on the OKX crypto exchange.
the project structure should be split into two crates. The first crate is a library crate that contains all the logic and data-structures necessary for the program to function correctly. The second crate is a binary crate that will contain the main function as well as any runtime configuration or helper functions that are required.
OKX supports most functionality through both REST and websocket API endpoints, our program will always use websockets where available (which should be everything in this project).
The architecture of the program should be in three major parts: ingress, core, and egress.
The ingress is responsible for connecting to the websocket, making subscriptions to the feeds we are concerned with and receiving messages from the websocket. When a message is received, the incoming data should be pushed onto a channel and sent to the core for processing.
The egress is responsible for sending outbound messages to the exchange via the tx half of the websocket connection created in the ingress. Outbound messages will be sent from the core to the egress via a channel.
Ingress
This component should be a type that executes inside a tokio async context. It connects to a websocket, ingests data and then immediately pushes that data into a channel for the core to process.
The ingress should support OPTIONAL subscriptions to the following channels:
Account channel:
https://www.okx.com/docs-v5/en/#trading-account-websocket-account-channel
Balance and position channel:
https://www.okx.com/docs-v5/en/#trading-account-websocket-balance-and-position-channel
Order channel:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-ws-order-channel
Fills channel:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-ws-fills-channel

Failing to subscribe to any of the channels that are configured should trigger a retry. Disconnecting the websocket and retrying all configured channels is acceptable.

In addition to handling pushed data from the above channels, it also needs to handle pushed responses from the following actions:

Place order:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-ws-place-order
Cancel order:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-ws-cancel-order
Modify order:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-ws-amend-order

Here is an rough outline for the ingress:
pub enum OkxRawAccountMsg {
    payload: bytes::Bytes,
    // ... any other needed fields
    // potentially information about the channel, but it might not be possible if
    // subscribing to all channels on the same websocket
}

pub enum OkxAccountIngressError {
    // ...
}

pub struct OkxAccountIngress {
    // ... (any data members required)
}


impl OkxAccountIngress
{
    async fn initialize<T>(&mut self, sender: &kanal::AsyncSender<T>) ->
    Result<(), OkxAccountIngressError>
    where T: Send,
          OkxRawAccountMsg: Into<T>
    {
        // any initialization that needs to occur
        todo!()
    }

    async fn start<T>(
        &self,
        sender: &kanal::AsyncSender<T>,
    ) -> Result<(), OkxAccountIngressError>
    where T: Send,
          OkxRawAccountMsg: Into<T>
    {

        loop {
            // receive data from websocket, push OkxRawAccountMsg to sender
            // this function should loop indefinitely unless there is an unrecoverable error
            todo!()
        }

        Ok(())
    }

}







Egress
The egress is also a component that executes inside a tokio async context.
It receives requests from a channel with pre-serialized payloads and sends them to the exchange over the appropriate connection.
As these requests come from the core, it will also need to push responses back into the ingress channel so that the core can understand whether the request was successful or not.

Requests that should be made over websocket:
Place order:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-ws-place-order
Cancel order:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-ws-cancel-order
Modify order:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-ws-amend-order

Requests that should be made over REST:
Order List:
https://www.okx.com/docs-v5/en/#order-book-trading-trade-get-order-list

Here is a rough outline for the egress:
pub enum OkxAccountApi {
    PlaceOrder,
    CancelOrder,
    // ... etc.
}

pub struct OkxAccountRequest {
    api: OkxAccountApi,
    payload: bytes::Bytes,
}

pub struct OkxAccountEgress {
    // ... (any data members required)
}

pub enum OkxAccountEgressError {
    // ...
}

pub struct OkxAccountEgressResponse {
    // ...
}


impl OkxAccountEgress {
    async fn initialize(&mut self) -> Result<(), OkxAccountEgressError> {
        // any initialization that needs to occur
        todo!()
    }

    async fn handle_msg<T>(
        &self,
        msg: &OkxAccountRequest,
        ingrs_sendr: &kanal::AsyncSender<T>,
    ) -> Result<(), OkxAccountEgressError>
    where T: Send,
          OkxAccountEgressResponse: Into<T>
    {
        // send request to exchange
        todo!()
    }
}





Core

In a production trading application, the core would have significant logic, however, for this exercise the core will be very simple and perform the following functions:
When a message is received on the ingress channel, after decoding and dispatching the message, the core will simply display the message to the user.
It will listen for commands from the user on stdin, serialize them appropriately, and push them to the egress channel to be sent to the exchange.
As the data from the exchange should be decoded in the core, it will be helpful to have a dispatcher object that encapsulates deserialization,

pub trait OkxAccountUpdateHandler {
    type Error;

    fn on_fill(&mut self, update: &OkxFillUpdate) -> Result<(), Self::Error>;
    fn on_place_order_response(&mut self, update: &OkxPlaceResp) -> Result<(), Self::Error>;
    // ... other supported updates
}

pub struct OkxAccountUpdateDispatcher<'a, T> {
    handler: &'a mut T,
}

impl<'a, T> OkxAccountUpdateDispatcher<'a, T>
where T: OkxAccountUpdateHandler
{
    pub fn dispatch(&mut self, update: &OkxRawAccountMsg) -> Result<(), T::Error> {
        // deserialize and dispatch update to handler
        todo!()
    }
}




Notes:
Both the ingress and egress components should be as stateless as possible.
Strings should be avoided wherever possible. Prefer enums instead when the string is predictable.
Do not prematurely pessimize. When the efficient or performant solution is a similar amount of work to the inefficient solution, prefer the efficient one.
Where a suboptimal solution is selected for ease of implementation, make note of it in comments and explain how it could be improved.
The thiserror crate should be used for errors. Errors that are handled programmatically (i.e. not just displayed to console) should be Copy, Clone.

Before coding, please familiarize yourself with the OKX API endpoints referenced above, and enumerate any questions that you need clarification on.



Spec v2

OKX Order Gateway - Project Specification
This specification describes the complete implementation requirements for an order gateway application interacting with the OKX crypto exchange. The implementation will use Rust, Tokio, and Tokio-Tungstenite for websocket management.
Project Structure
Split the application into two crates:
Library Crate: Contains all core logic and data structures.
Binary Crate: Contains the main function, runtime configurations, and helper utilities.
Architecture Overview
The architecture comprises three components:
Ingress: Receives websocket messages.
Core: Processes messages and handles user interactions.
Egress: Sends requests to the exchange.

Add note about communication via channels

Websocket and REST Connections
Only one websocket connection is necessary for all websocket-based interactions. This connection is shared across ingress and egress components using an asynchronous mutex (tokio::sync::Mutex) wrapped in an Arc.
REST calls are only for requests without websocket endpoints, such as fetching the order list.
Ingress
Responsibilities:
Establish a websocket connection.
Authenticate via websocket.
Subscribe (and maintain subscriptions) to optional channels:
Account
Balance and position
Order
Fills
Handle responses from actions:
Place order
Cancel order
Modify order
Immediately forward raw websocket payloads to the core via an asynchronous channel.
Detect disconnections and automatically manage reconnections and re-subscriptions.
Rough outline of ingress:
pub enum OkxRawAccountMsg {
    payload: bytes::Bytes,
}

pub enum OkxAccountIngressError {}

pub struct OkxAccountIngress {}

impl OkxAccountIngress {
    async fn initialize<T>(&mut self, sender: &kanal::AsyncSender<T>) ->
    Result<(), OkxAccountIngressError>
    where T: Send,
          OkxRawAccountMsg: Into<T>
    {
        todo!()
    }

    async fn start<T>(
        &self,
        sender: &kanal::AsyncSender<T>,
    ) -> Result<(), OkxAccountIngressError>
    where T: Send,
          OkxRawAccountMsg: Into<T>
    {
        loop {
            todo!()
        }
    }
}

Egress
Responsibilities:
Receive serialized payloads from the core via a channel.
For websocket interactions, directly send payloads through the shared websocket connection.
Handle REST API requests using async/await semantics without spawning new tasks. The REST request-response cycle should be managed within the egress loop itself for simplicity and minimal latency.
Responses from REST endpoints should be immediately relayed back to the core through a the aforementioned ingress channel.
Rough outline of egress:
pub enum OkxAccountApi {
    PlaceOrder,
    CancelOrder,
}

pub struct OkxAccountRequest {
    api: OkxAccountApi,
    payload: bytes::Bytes,
}

pub struct OkxAccountEgress {}

pub enum OkxAccountEgressError {}

pub struct OkxAccountEgressResponse {}

impl OkxAccountEgress {
    async fn initialize(&mut self) -> Result<(), OkxAccountEgressError> {
        todo!()
    }

    async fn handle_msg<T>(
        &self,
        msg: &OkxAccountRequest,
        ingrs_sendr: &kanal::AsyncSender<T>,
    ) -> Result<(), OkxAccountEgressError>
    where T: Send,
          OkxAccountEgressResponse: Into<T>
    {
        todo!()
    }
}

Core
Responsibilities:
Receive and deserialize messages from ingress.
Implement a dispatcher to handle different incoming message types.
Handle serialization of user commands and forward them to egress.
Display processed messages to the user.
Dispatcher and Parsing
Message parsing happens within the core, not the ingress.
Use serde for parsing, despite potential inefficiency, to prioritize clarity and maintainability.
Implement an abstract dispatcher to encapsulate parsing logic:
pub trait OkxAccountUpdateHandler {
    type Error;

    fn on_fill(&mut self, update: &OkxFillUpdate) -> Result<(), Self::Error>;
    fn on_place_order_response(&mut self, update: &OkxPlaceResp) -> Result<(), Self::Error>;
}

pub struct OkxAccountUpdateDispatcher<'a, T> {
    handler: &'a mut T,
}

impl<'a, T> OkxAccountUpdateDispatcher<'a, T>
where
    T: OkxAccountUpdateHandler,
{
    pub fn dispatch(&mut self, update: &OkxRawAccountMsg) -> Result<(), T::Error> {
        todo!()
    }
}

Error Handling
Use the thiserror crate for defining errors.
All errors must be logged clearly.
Automatically retry websocket connections and subscriptions indefinitely, logging each attempt.
REST request failures should be logged and reported back to the core for potential user notification.
Performance and Latency
All blocking operations are strictly forbidden in the ingress and egress.
Use Tokio Mutexes to ensure non-blocking asynchronous locks.
Prioritize minimizing latency between message reception/transmission and channel interactions.
Authentication
Websocket authentication requires API key, passphrase, and secretKey. Store these securely and configure them clearly.
Multiple account logins on a single websocket are supported. Ensure clear management of credentials.
Dependencies
Tokio for asynchronous execution.
Tokio-Tungstenite for websocket connections.
Serde for serialization and deserialization.
Kanal channels for inter-component communication.
thiserror for error definitions.
Daily Workflow
The developer must push code daily to facilitate progress monitoring.
Additional Notes
Avoid premature optimization but clearly document performance trade-offs.
Prefer enums over strings when possible.
Clearly structure code to maintain separation of concerns.
